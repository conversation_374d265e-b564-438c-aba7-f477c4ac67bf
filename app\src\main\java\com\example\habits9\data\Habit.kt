package com.example.habits9.data

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.LocalDate
import java.util.UUID

@Entity(tableName = "habits")
data class Habit(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val description: String = "",
    val creationDate: Long = System.currentTimeMillis(), // Using timestamp
    val currentStreak: Int = 0,
    val completionDatesJson: String = "{}", // Using JSON string instead of Map
    val uuid: String = UUID.randomUUID().toString().replace("-", ""),
    val isArchived: Boolean = false,
    val position: Int = 0,
    val color: Int = 8, // Default color index
    val type: Int = HabitType.YES_NO.value, // Habit type (YES_NO or NUMERICAL)
    val targetType: Int = NumericalHabitType.AT_LEAST.value, // For numerical habits
    val targetValue: Double = 0.0, // Target value for numerical habits
    val unit: String = "" // Unit of measurement for numerical habits (e.g., "glasses", "km")
) {
    val habitType: HabitType
        get() = HabitType.fromInt(type)
    
    val numericalHabitType: NumericalHabitType
        get() = NumericalHabitType.fromInt(targetType)
    
    val isNumerical: Boolean
        get() = habitType == HabitType.NUMERICAL
}
