package com.example.habits9.ui.createmeasurablehabit

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

// Design System Colors - Dark Theme
private val DarkBackground = Color(0xFF121826)
private val SurfaceVariantDark = Color(0xFF1A202C)
private val TextPrimary = Color(0xFFE2E8F0)
private val TextSecondary = Color(0xFFA0AEC0)
private val AccentPrimary = Color(0xFF81E6D9)
private val DividerColor = Color(0xFF2D3748)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateMeasurableHabitScreen(
    onBackClick: () -> Unit = {},
    onSaveClick: () -> Unit = {}
) {
    var name by remember { mutableStateOf("") }
    var question by remember { mutableStateOf("") }
    var unit by remember { mutableStateOf("") }
    var target by remember { mutableStateOf("") }
    var notes by remember { mutableStateOf("") }
    var selectedColor by remember { mutableStateOf(AccentPrimary) }
    var selectedFrequency by remember { mutableStateOf("Every Day") }
    var selectedTargetType by remember { mutableStateOf("At Least") }
    var showFrequencyDropdown by remember { mutableStateOf(false) }
    var showTargetTypeDropdown by remember { mutableStateOf(false) }

    val frequencyOptions = listOf("Every Day", "Every Week", "Every Month")
    val targetTypeOptions = listOf("At Least", "At Most")

    Scaffold(
        containerColor = DarkBackground,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Create habit",
                        color = TextPrimary,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = TextPrimary
                        )
                    }
                },
                actions = {
                    TextButton(onClick = onSaveClick) {
                        Text(
                            text = "SAVE",
                            color = AccentPrimary,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = SurfaceVariantDark
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.height(24.dp))

            // Habit Section Selector
            OutlinedTextField(
                value = "Morning",
                onValueChange = { },
                label = { Text("Habit Section", color = TextSecondary) },
                modifier = Modifier.fillMaxWidth(),
                enabled = false,
                trailingIcon = {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "Select habit section",
                        tint = TextSecondary
                    )
                },
                colors = OutlinedTextFieldDefaults.colors(
                    disabledTextColor = TextPrimary,
                    disabledBorderColor = DividerColor,
                    disabledLabelColor = TextSecondary
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Name Field with Color Selector
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Name", color = TextSecondary) },
                    placeholder = { Text("e.g. Run", color = TextSecondary) },
                    modifier = Modifier.weight(1f),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = TextPrimary,
                        unfocusedTextColor = TextPrimary,
                        focusedBorderColor = AccentPrimary,
                        unfocusedBorderColor = DividerColor,
                        cursorColor = AccentPrimary
                    )
                )
                Spacer(modifier = Modifier.width(12.dp))
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .background(selectedColor, CircleShape)
                        .clickable { /* Color picker would go here */ }
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Question Field
            OutlinedTextField(
                value = question,
                onValueChange = { question = it },
                label = { Text("Question", color = TextSecondary) },
                placeholder = { Text("e.g. How many miles did you run today?", color = TextSecondary) },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = TextPrimary,
                    unfocusedTextColor = TextPrimary,
                    focusedBorderColor = AccentPrimary,
                    unfocusedBorderColor = DividerColor,
                    cursorColor = AccentPrimary
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Unit Field
            OutlinedTextField(
                value = unit,
                onValueChange = { unit = it },
                label = { Text("Unit", color = TextSecondary) },
                placeholder = { Text("e.g. miles", color = TextSecondary) },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = TextPrimary,
                    unfocusedTextColor = TextPrimary,
                    focusedBorderColor = AccentPrimary,
                    unfocusedBorderColor = DividerColor,
                    cursorColor = AccentPrimary
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Target Field
            OutlinedTextField(
                value = target,
                onValueChange = { target = it },
                label = { Text("Target", color = TextSecondary) },
                placeholder = { Text("e.g. 15", color = TextSecondary) },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = TextPrimary,
                    unfocusedTextColor = TextPrimary,
                    focusedBorderColor = AccentPrimary,
                    unfocusedBorderColor = DividerColor,
                    cursorColor = AccentPrimary
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Frequency Dropdown
            ExposedDropdownMenuBox(
                expanded = showFrequencyDropdown,
                onExpandedChange = { showFrequencyDropdown = !showFrequencyDropdown }
            ) {
                OutlinedTextField(
                    value = selectedFrequency,
                    onValueChange = { },
                    readOnly = true,
                    label = { Text("Frequency", color = TextSecondary) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor(),
                    trailingIcon = {
                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = showFrequencyDropdown)
                    },
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = TextPrimary,
                        unfocusedTextColor = TextPrimary,
                        focusedBorderColor = AccentPrimary,
                        unfocusedBorderColor = DividerColor
                    )
                )
                ExposedDropdownMenu(
                    expanded = showFrequencyDropdown,
                    onDismissRequest = { showFrequencyDropdown = false },
                    modifier = Modifier.background(SurfaceVariantDark)
                ) {
                    frequencyOptions.forEach { option ->
                        DropdownMenuItem(
                            text = { Text(option, color = TextPrimary) },
                            onClick = {
                                selectedFrequency = option
                                showFrequencyDropdown = false
                            }
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Target Type Dropdown
            ExposedDropdownMenuBox(
                expanded = showTargetTypeDropdown,
                onExpandedChange = { showTargetTypeDropdown = !showTargetTypeDropdown }
            ) {
                OutlinedTextField(
                    value = selectedTargetType,
                    onValueChange = { },
                    readOnly = true,
                    label = { Text("Target Type", color = TextSecondary) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor(),
                    trailingIcon = {
                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = showTargetTypeDropdown)
                    },
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = TextPrimary,
                        unfocusedTextColor = TextPrimary,
                        focusedBorderColor = AccentPrimary,
                        unfocusedBorderColor = DividerColor
                    )
                )
                ExposedDropdownMenu(
                    expanded = showTargetTypeDropdown,
                    onDismissRequest = { showTargetTypeDropdown = false },
                    modifier = Modifier.background(SurfaceVariantDark)
                ) {
                    targetTypeOptions.forEach { option ->
                        DropdownMenuItem(
                            text = { Text(option, color = TextPrimary) },
                            onClick = {
                                selectedTargetType = option
                                showTargetTypeDropdown = false
                            }
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Reminder Field
            OutlinedTextField(
                value = "Off",
                onValueChange = { },
                label = { Text("Reminder", color = TextSecondary) },
                modifier = Modifier.fillMaxWidth(),
                enabled = false,
                trailingIcon = {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "Select reminder",
                        tint = TextSecondary
                    )
                },
                colors = OutlinedTextFieldDefaults.colors(
                    disabledTextColor = TextPrimary,
                    disabledBorderColor = DividerColor,
                    disabledLabelColor = TextSecondary
                )
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Notes Field
            OutlinedTextField(
                value = notes,
                onValueChange = { notes = it },
                label = { Text("Notes", color = TextSecondary) },
                placeholder = { Text("(Optional)", color = TextSecondary) },
                modifier = Modifier.fillMaxWidth(),
                minLines = 3,
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = TextPrimary,
                    unfocusedTextColor = TextPrimary,
                    focusedBorderColor = AccentPrimary,
                    unfocusedBorderColor = DividerColor,
                    cursorColor = AccentPrimary
                )
            )

            Spacer(modifier = Modifier.height(24.dp))
        }
    }
}