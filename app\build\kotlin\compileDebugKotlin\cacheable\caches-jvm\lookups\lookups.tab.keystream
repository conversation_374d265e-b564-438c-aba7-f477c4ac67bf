  Activity android.app  Application android.app  Build android.app.Activity  CreateMeasurableHabitScreen android.app.Activity  CreateYesNoHabitScreen android.app.Activity  HabitDetailsScreen android.app.Activity  HabitTypeSelectionScreen android.app.Activity  
HomeScreen android.app.Activity  ManageSectionsScreen android.app.Activity  NavHost android.app.Activity  UHabits_99Theme android.app.Activity  
composable android.app.Activity  onCreate android.app.Activity  rememberNavController android.app.Activity  
setContent android.app.Activity  Context android.content  Build android.content.Context  CreateMeasurableHabitScreen android.content.Context  CreateYesNoHabitScreen android.content.Context  HabitDetailsScreen android.content.Context  HabitTypeSelectionScreen android.content.Context  
HomeScreen android.content.Context  ManageSectionsScreen android.content.Context  NavHost android.content.Context  UHabits_99Theme android.content.Context  
composable android.content.Context  rememberNavController android.content.Context  
setContent android.content.Context  Build android.content.ContextWrapper  CreateMeasurableHabitScreen android.content.ContextWrapper  CreateYesNoHabitScreen android.content.ContextWrapper  HabitDetailsScreen android.content.ContextWrapper  HabitTypeSelectionScreen android.content.ContextWrapper  
HomeScreen android.content.ContextWrapper  ManageSectionsScreen android.content.ContextWrapper  NavHost android.content.ContextWrapper  UHabits_99Theme android.content.ContextWrapper  
composable android.content.ContextWrapper  rememberNavController android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  	getString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  	getString android.os.Bundle  Build  android.view.ContextThemeWrapper  CreateMeasurableHabitScreen  android.view.ContextThemeWrapper  CreateYesNoHabitScreen  android.view.ContextThemeWrapper  HabitDetailsScreen  android.view.ContextThemeWrapper  HabitTypeSelectionScreen  android.view.ContextThemeWrapper  
HomeScreen  android.view.ContextThemeWrapper  ManageSectionsScreen  android.view.ContextThemeWrapper  NavHost  android.view.ContextThemeWrapper  UHabits_99Theme  android.view.ContextThemeWrapper  
composable  android.view.ContextThemeWrapper  rememberNavController  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Build #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  CreateMeasurableHabitScreen #androidx.activity.ComponentActivity  CreateYesNoHabitScreen #androidx.activity.ComponentActivity  HabitDetailsScreen #androidx.activity.ComponentActivity  HabitTypeSelectionScreen #androidx.activity.ComponentActivity  
HomeScreen #androidx.activity.ComponentActivity  ManageSectionsScreen #androidx.activity.ComponentActivity  NavHost #androidx.activity.ComponentActivity  RequiresApi #androidx.activity.ComponentActivity  UHabits_99Theme #androidx.activity.ComponentActivity  
composable #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  rememberNavController #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  Build -androidx.activity.ComponentActivity.Companion  CreateMeasurableHabitScreen -androidx.activity.ComponentActivity.Companion  CreateYesNoHabitScreen -androidx.activity.ComponentActivity.Companion  HabitDetailsScreen -androidx.activity.ComponentActivity.Companion  HabitTypeSelectionScreen -androidx.activity.ComponentActivity.Companion  
HomeScreen -androidx.activity.ComponentActivity.Companion  ManageSectionsScreen -androidx.activity.ComponentActivity.Companion  NavHost -androidx.activity.ComponentActivity.Companion  UHabits_99Theme -androidx.activity.ComponentActivity.Companion  
composable -androidx.activity.ComponentActivity.Companion  rememberNavController -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  RequiresApi androidx.annotation  AnimatedContentScope androidx.compose.animation  CreateMeasurableHabitScreen /androidx.compose.animation.AnimatedContentScope  CreateYesNoHabitScreen /androidx.compose.animation.AnimatedContentScope  HabitDetailsScreen /androidx.compose.animation.AnimatedContentScope  HabitTypeSelectionScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  ManageSectionsScreen /androidx.compose.animation.AnimatedContentScope  Canvas androidx.compose.foundation  ScrollState androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  
AccentPrimary "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  DividerColor "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExposedDropdownMenuBox "androidx.compose.foundation.layout  ExposedDropdownMenuDefaults "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  FrequencyOption "androidx.compose.foundation.layout  HabitSection "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  ManageSectionsViewModel "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  OutlinedTextFieldDefaults "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  RadioButton "androidx.compose.foundation.layout  RadioButtonDefaults "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  
SectionColors "androidx.compose.foundation.layout  SectionListItem "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  SurfaceVariantDark "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TextPrimary "androidx.compose.foundation.layout  
TextSecondary "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TopAppBarDefaults "androidx.compose.foundation.layout  TrailingIcon "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  colors "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  topAppBarColors "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceAround .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  
AccentPrimary +androidx.compose.foundation.layout.BoxScope  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  CompletionIndicator +androidx.compose.foundation.layout.BoxScope  DropdownMenu +androidx.compose.foundation.layout.BoxScope  DropdownMenuItem +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
LazyColumn +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  MoreVert +androidx.compose.foundation.layout.BoxScope  
PaddingValues +androidx.compose.foundation.layout.BoxScope  SectionListItem +androidx.compose.foundation.layout.BoxScope  SurfaceVariantDark +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  TextPrimary +androidx.compose.foundation.layout.BoxScope  
TextSecondary +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  items +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  
AccentPrimary .androidx.compose.foundation.layout.ColumnScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  AllHabitsDropdown .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  CustomHeader .androidx.compose.foundation.layout.ColumnScope  DividerColor .androidx.compose.foundation.layout.ColumnScope  DropdownMenu .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuBox .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuDefaults .androidx.compose.foundation.layout.ColumnScope  
FontFamily .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  FrequencyOption .androidx.compose.foundation.layout.ColumnScope  HabitGridRow .androidx.compose.foundation.layout.ColumnScope  HabitGridWithData .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowDown .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  
MetricView .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  MoreVert .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  OutlinedTextFieldDefaults .androidx.compose.foundation.layout.ColumnScope  OverviewSection .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  
SectionColors .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  SubHeaderRow .androidx.compose.foundation.layout.ColumnScope  SurfaceVariantDark .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  TextPrimary .androidx.compose.foundation.layout.ColumnScope  
TextSecondary .androidx.compose.foundation.layout.ColumnScope  TrailingIcon .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  split .androidx.compose.foundation.layout.ColumnScope  	uppercase .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  
AccentPrimary +androidx.compose.foundation.layout.RowScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  AllHabitsDropdown +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  CompletionIndicator +androidx.compose.foundation.layout.RowScope  	DateRange +androidx.compose.foundation.layout.RowScope  DividerColor +androidx.compose.foundation.layout.RowScope  DropdownMenu +androidx.compose.foundation.layout.RowScope  DropdownMenuItem +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  
FontFamily +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  KeyboardArrowDown +androidx.compose.foundation.layout.RowScope  
MetricView +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  MoreVert +androidx.compose.foundation.layout.RowScope  NotificationsOff +androidx.compose.foundation.layout.RowScope  OutlinedTextField +androidx.compose.foundation.layout.RowScope  OutlinedTextFieldDefaults +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  RadioButtonDefaults +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  SurfaceVariantDark +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  	TextAlign +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  TextPrimary +androidx.compose.foundation.layout.RowScope  
TextSecondary +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  	clickable +androidx.compose.foundation.layout.RowScope  colors +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  spacedBy +androidx.compose.foundation.layout.RowScope  split +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Box .androidx.compose.foundation.lazy.LazyItemScope  CircleShape .androidx.compose.foundation.lazy.LazyItemScope  Color .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  SectionListItem .androidx.compose.foundation.lazy.LazyItemScope  
background .androidx.compose.foundation.lazy.LazyItemScope  	clickable .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  Box .androidx.compose.foundation.lazy.LazyListScope  CircleShape .androidx.compose.foundation.lazy.LazyListScope  Color .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  
SectionColors .androidx.compose.foundation.lazy.LazyListScope  SectionListItem .androidx.compose.foundation.lazy.LazyListScope  
background .androidx.compose.foundation.lazy.LazyListScope  	clickable .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  	DateRange ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowDown ,androidx.compose.material.icons.Icons.Filled  MoreVert ,androidx.compose.material.icons.Icons.Filled  NotificationsOff ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  	DateRange &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  KeyboardArrowDown &androidx.compose.material.icons.filled  MoreVert &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  NotificationsOff &androidx.compose.material.icons.filled  
AccentPrimary androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Boolean androidx.compose.material3  Box androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  DividerColor androidx.compose.material3  DropdownMenu androidx.compose.material3  DropdownMenuItem androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuBox androidx.compose.material3  ExposedDropdownMenuBoxScope androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  
FontWeight androidx.compose.material3  FrequencyOption androidx.compose.material3  HabitSection androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Int androidx.compose.material3  
LazyColumn androidx.compose.material3  LazyRow androidx.compose.material3  ManageSectionsViewModel androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  
PaddingValues androidx.compose.material3  RadioButton androidx.compose.material3  RadioButtonColors androidx.compose.material3  RadioButtonDefaults androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  
SectionColors androidx.compose.material3  SectionListItem androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  SurfaceVariantDark androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  TextFieldColors androidx.compose.material3  TextPrimary androidx.compose.material3  
TextSecondary androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarColors androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  TrailingIcon androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  align androidx.compose.material3  
background androidx.compose.material3  
cardColors androidx.compose.material3  	clickable androidx.compose.material3  collectAsState androidx.compose.material3  colors androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNotBlank androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  topAppBarColors androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
AccentPrimary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  DividerColor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  DropdownMenuItem 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenu 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenuDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Modifier 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextField 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextFieldDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  SurfaceVariantDark 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Text 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TextPrimary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
TextSecondary 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
background 6androidx.compose.material3.ExposedDropdownMenuBoxScope  colors 6androidx.compose.material3.ExposedDropdownMenuBoxScope  fillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
menuAnchor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuDefaults  colors 4androidx.compose.material3.OutlinedTextFieldDefaults  colors .androidx.compose.material3.RadioButtonDefaults  topAppBarColors ,androidx.compose.material3.TopAppBarDefaults  
AccentPrimary androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  DividerColor androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExposedDropdownMenuBox androidx.compose.runtime  ExposedDropdownMenuDefaults androidx.compose.runtime  
FontWeight androidx.compose.runtime  FrequencyOption androidx.compose.runtime  HabitSection androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Int androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyRow androidx.compose.runtime  ManageSectionsViewModel androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  OutlinedTextFieldDefaults androidx.compose.runtime  
PaddingValues androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RadioButton androidx.compose.runtime  RadioButtonDefaults androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  
SectionColors androidx.compose.runtime  SectionListItem androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  SurfaceVariantDark androidx.compose.runtime  Text androidx.compose.runtime  
TextButton androidx.compose.runtime  TextPrimary androidx.compose.runtime  
TextSecondary androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TopAppBarDefaults androidx.compose.runtime  TrailingIcon androidx.compose.runtime  Unit androidx.compose.runtime  align androidx.compose.runtime  
background androidx.compose.runtime  
cardColors androidx.compose.runtime  	clickable androidx.compose.runtime  collectAsState androidx.compose.runtime  colors androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  
isNotBlank androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  topAppBarColors androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  
menuAnchor androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  then androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  
background &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Size androidx.compose.ui.geometry  minDimension !androidx.compose.ui.geometry.Size  Color androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  value "androidx.compose.ui.graphics.Color  White ,androidx.compose.ui.graphics.Color.Companion  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  
AccentPrimary 0androidx.compose.ui.graphics.drawscope.DrawScope  Stroke 0androidx.compose.ui.graphics.drawscope.DrawScope  
TextSecondary 0androidx.compose.ui.graphics.drawscope.DrawScope  dp 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx 0androidx.compose.ui.graphics.drawscope.DrawScope  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  	Monospace (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  toPx  androidx.compose.ui.unit.Density  toPx androidx.compose.ui.unit.Dp  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  CreateMeasurableHabitScreen #androidx.core.app.ComponentActivity  CreateYesNoHabitScreen #androidx.core.app.ComponentActivity  HabitDetailsScreen #androidx.core.app.ComponentActivity  HabitTypeSelectionScreen #androidx.core.app.ComponentActivity  
HomeScreen #androidx.core.app.ComponentActivity  ManageSectionsScreen #androidx.core.app.ComponentActivity  NavHost #androidx.core.app.ComponentActivity  RequiresApi #androidx.core.app.ComponentActivity  UHabits_99Theme #androidx.core.app.ComponentActivity  
composable #androidx.core.app.ComponentActivity  rememberNavController #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  	arguments %androidx.navigation.NavBackStackEntry  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  CreateMeasurableHabitScreen #androidx.navigation.NavGraphBuilder  CreateYesNoHabitScreen #androidx.navigation.NavGraphBuilder  HabitDetailsScreen #androidx.navigation.NavGraphBuilder  HabitTypeSelectionScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  ManageSectionsScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Flow 
androidx.room  Habit 
androidx.room  HabitSection 
androidx.room  Insert 
androidx.room  List 
androidx.room  Long 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  Builder androidx.room.RoomDatabase  HabitDao androidx.room.RoomDatabase  HabitSectionDao androidx.room.RoomDatabase  
addMigrations "androidx.room.RoomDatabase.Builder  build "androidx.room.RoomDatabase.Builder  	Migration androidx.room.migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  Application com.example.habits9  HabitsApplication com.example.habits9  HiltAndroidApp com.example.habits9  AT_LEAST com.example.habits9.data  AT_MOST com.example.habits9.data  Boolean com.example.habits9.data  Dao com.example.habits9.data  Database com.example.habits9.data  Delete com.example.habits9.data  Double com.example.habits9.data  Entity com.example.habits9.data  Flow com.example.habits9.data  Habit com.example.habits9.data  HabitDao com.example.habits9.data  
HabitDatabase com.example.habits9.data  HabitRepository com.example.habits9.data  HabitSection com.example.habits9.data  HabitSectionDao com.example.habits9.data  HabitSectionRepository com.example.habits9.data  	HabitType com.example.habits9.data  IllegalStateException com.example.habits9.data  Inject com.example.habits9.data  Insert com.example.habits9.data  Int com.example.habits9.data  List com.example.habits9.data  Long com.example.habits9.data  	NUMERICAL com.example.habits9.data  NumericalHabitType com.example.habits9.data  OnConflictStrategy com.example.habits9.data  
PrimaryKey com.example.habits9.data  Query com.example.habits9.data  RoomDatabase com.example.habits9.data  	Singleton com.example.habits9.data  String com.example.habits9.data  System com.example.habits9.data  UUID com.example.habits9.data  Update com.example.habits9.data  YES_NO com.example.habits9.data  fromInt com.example.habits9.data  replace com.example.habits9.data  	HabitType com.example.habits9.data.Habit  NumericalHabitType com.example.habits9.data.Habit  fromInt com.example.habits9.data.Habit  	habitType com.example.habits9.data.Habit  id com.example.habits9.data.Habit  name com.example.habits9.data.Habit  
targetType com.example.habits9.data.Habit  type com.example.habits9.data.Habit  OnConflictStrategy !com.example.habits9.data.HabitDao  deleteHabit !com.example.habits9.data.HabitDao  getAllHabits !com.example.habits9.data.HabitDao  getHabitById !com.example.habits9.data.HabitDao  insertHabit !com.example.habits9.data.HabitDao  updateHabit !com.example.habits9.data.HabitDao  habitDao &com.example.habits9.data.HabitDatabase  habitSectionDao &com.example.habits9.data.HabitDatabase  getAllHabits (com.example.habits9.data.HabitRepository  habitDao (com.example.habits9.data.HabitRepository  insertHabit (com.example.habits9.data.HabitRepository  color %com.example.habits9.data.HabitSection  name %com.example.habits9.data.HabitSection  OnConflictStrategy (com.example.habits9.data.HabitSectionDao  deleteHabitSection (com.example.habits9.data.HabitSectionDao  getAllHabitSections (com.example.habits9.data.HabitSectionDao  insertHabitSection (com.example.habits9.data.HabitSectionDao  updateHabitSection (com.example.habits9.data.HabitSectionDao  getAllHabitSections /com.example.habits9.data.HabitSectionRepository  habitSectionDao /com.example.habits9.data.HabitSectionRepository  insertHabitSection /com.example.habits9.data.HabitSectionRepository  	Companion "com.example.habits9.data.HabitType  	HabitType "com.example.habits9.data.HabitType  IllegalStateException "com.example.habits9.data.HabitType  Int "com.example.habits9.data.HabitType  	NUMERICAL "com.example.habits9.data.HabitType  YES_NO "com.example.habits9.data.HabitType  fromInt "com.example.habits9.data.HabitType  value "com.example.habits9.data.HabitType  IllegalStateException ,com.example.habits9.data.HabitType.Companion  	NUMERICAL ,com.example.habits9.data.HabitType.Companion  YES_NO ,com.example.habits9.data.HabitType.Companion  fromInt ,com.example.habits9.data.HabitType.Companion  AT_LEAST +com.example.habits9.data.NumericalHabitType  AT_MOST +com.example.habits9.data.NumericalHabitType  	Companion +com.example.habits9.data.NumericalHabitType  IllegalStateException +com.example.habits9.data.NumericalHabitType  Int +com.example.habits9.data.NumericalHabitType  NumericalHabitType +com.example.habits9.data.NumericalHabitType  fromInt +com.example.habits9.data.NumericalHabitType  value +com.example.habits9.data.NumericalHabitType  AT_LEAST 5com.example.habits9.data.NumericalHabitType.Companion  AT_MOST 5com.example.habits9.data.NumericalHabitType.Companion  IllegalStateException 5com.example.habits9.data.NumericalHabitType.Companion  fromInt 5com.example.habits9.data.NumericalHabitType.Companion  ApplicationContext com.example.habits9.di  Context com.example.habits9.di  DatabaseModule com.example.habits9.di  HabitDao com.example.habits9.di  
HabitDatabase com.example.habits9.di  HabitRepository com.example.habits9.di  HabitSectionDao com.example.habits9.di  HabitSectionRepository com.example.habits9.di  	InstallIn com.example.habits9.di  	Migration com.example.habits9.di  Module com.example.habits9.di  Provides com.example.habits9.di  Room com.example.habits9.di  	Singleton com.example.habits9.di  SingletonComponent com.example.habits9.di  SupportSQLiteDatabase com.example.habits9.di  databaseBuilder com.example.habits9.di  java com.example.habits9.di  
trimIndent com.example.habits9.di  
HabitDatabase %com.example.habits9.di.DatabaseModule  HabitRepository %com.example.habits9.di.DatabaseModule  HabitSectionRepository %com.example.habits9.di.DatabaseModule  
MIGRATION_3_4 %com.example.habits9.di.DatabaseModule  Room %com.example.habits9.di.DatabaseModule  databaseBuilder %com.example.habits9.di.DatabaseModule  java %com.example.habits9.di.DatabaseModule  
trimIndent %com.example.habits9.di.DatabaseModule  Boolean com.example.habits9.ui  Double com.example.habits9.ui  Habit com.example.habits9.ui  HabitRepository com.example.habits9.ui  	HabitType com.example.habits9.ui  
HiltViewModel com.example.habits9.ui  Inject com.example.habits9.ui  List com.example.habits9.ui  MainUiState com.example.habits9.ui  
MainViewModel com.example.habits9.ui  MutableStateFlow com.example.habits9.ui  NumericalHabitType com.example.habits9.ui  	StateFlow com.example.habits9.ui  String com.example.habits9.ui  System com.example.habits9.ui  	ViewModel com.example.habits9.ui  asStateFlow com.example.habits9.ui  	emptyList com.example.habits9.ui  habitRepository com.example.habits9.ui  isBlank com.example.habits9.ui  launch com.example.habits9.ui  plus com.example.habits9.ui  trim com.example.habits9.ui  copy "com.example.habits9.ui.MainUiState  habits "com.example.habits9.ui.MainUiState  Habit $com.example.habits9.ui.MainViewModel  	HabitType $com.example.habits9.ui.MainViewModel  MainUiState $com.example.habits9.ui.MainViewModel  MutableStateFlow $com.example.habits9.ui.MainViewModel  NumericalHabitType $com.example.habits9.ui.MainViewModel  System $com.example.habits9.ui.MainViewModel  _uiState $com.example.habits9.ui.MainViewModel  asStateFlow $com.example.habits9.ui.MainViewModel  	emptyList $com.example.habits9.ui.MainViewModel  habitRepository $com.example.habits9.ui.MainViewModel  isBlank $com.example.habits9.ui.MainViewModel  launch $com.example.habits9.ui.MainViewModel  plus $com.example.habits9.ui.MainViewModel  trim $com.example.habits9.ui.MainViewModel  uiState $com.example.habits9.ui.MainViewModel  viewModelScope $com.example.habits9.ui.MainViewModel  
AccentPrimary ,com.example.habits9.ui.createmeasurablehabit  	Alignment ,com.example.habits9.ui.createmeasurablehabit  Box ,com.example.habits9.ui.createmeasurablehabit  CircleShape ,com.example.habits9.ui.createmeasurablehabit  Column ,com.example.habits9.ui.createmeasurablehabit  
Composable ,com.example.habits9.ui.createmeasurablehabit  CreateMeasurableHabitScreen ,com.example.habits9.ui.createmeasurablehabit  DarkBackground ,com.example.habits9.ui.createmeasurablehabit  DividerColor ,com.example.habits9.ui.createmeasurablehabit  DropdownMenuItem ,com.example.habits9.ui.createmeasurablehabit  ExperimentalMaterial3Api ,com.example.habits9.ui.createmeasurablehabit  ExposedDropdownMenuBox ,com.example.habits9.ui.createmeasurablehabit  ExposedDropdownMenuDefaults ,com.example.habits9.ui.createmeasurablehabit  
FontWeight ,com.example.habits9.ui.createmeasurablehabit  Icon ,com.example.habits9.ui.createmeasurablehabit  
IconButton ,com.example.habits9.ui.createmeasurablehabit  Icons ,com.example.habits9.ui.createmeasurablehabit  Modifier ,com.example.habits9.ui.createmeasurablehabit  OptIn ,com.example.habits9.ui.createmeasurablehabit  OutlinedTextField ,com.example.habits9.ui.createmeasurablehabit  OutlinedTextFieldDefaults ,com.example.habits9.ui.createmeasurablehabit  Row ,com.example.habits9.ui.createmeasurablehabit  Scaffold ,com.example.habits9.ui.createmeasurablehabit  Spacer ,com.example.habits9.ui.createmeasurablehabit  SurfaceVariantDark ,com.example.habits9.ui.createmeasurablehabit  Text ,com.example.habits9.ui.createmeasurablehabit  
TextButton ,com.example.habits9.ui.createmeasurablehabit  TextPrimary ,com.example.habits9.ui.createmeasurablehabit  
TextSecondary ,com.example.habits9.ui.createmeasurablehabit  	TopAppBar ,com.example.habits9.ui.createmeasurablehabit  TopAppBarDefaults ,com.example.habits9.ui.createmeasurablehabit  TrailingIcon ,com.example.habits9.ui.createmeasurablehabit  Unit ,com.example.habits9.ui.createmeasurablehabit  
background ,com.example.habits9.ui.createmeasurablehabit  	clickable ,com.example.habits9.ui.createmeasurablehabit  colors ,com.example.habits9.ui.createmeasurablehabit  fillMaxSize ,com.example.habits9.ui.createmeasurablehabit  fillMaxWidth ,com.example.habits9.ui.createmeasurablehabit  forEach ,com.example.habits9.ui.createmeasurablehabit  getValue ,com.example.habits9.ui.createmeasurablehabit  height ,com.example.habits9.ui.createmeasurablehabit  listOf ,com.example.habits9.ui.createmeasurablehabit  mutableStateOf ,com.example.habits9.ui.createmeasurablehabit  padding ,com.example.habits9.ui.createmeasurablehabit  provideDelegate ,com.example.habits9.ui.createmeasurablehabit  remember ,com.example.habits9.ui.createmeasurablehabit  setValue ,com.example.habits9.ui.createmeasurablehabit  size ,com.example.habits9.ui.createmeasurablehabit  topAppBarColors ,com.example.habits9.ui.createmeasurablehabit  weight ,com.example.habits9.ui.createmeasurablehabit  width ,com.example.habits9.ui.createmeasurablehabit  
AccentPrimary 'com.example.habits9.ui.createyesnohabit  AlertDialog 'com.example.habits9.ui.createyesnohabit  	Alignment 'com.example.habits9.ui.createyesnohabit  Boolean 'com.example.habits9.ui.createyesnohabit  Box 'com.example.habits9.ui.createyesnohabit  CircleShape 'com.example.habits9.ui.createyesnohabit  Column 'com.example.habits9.ui.createyesnohabit  
Composable 'com.example.habits9.ui.createyesnohabit  CreateYesNoHabitScreen 'com.example.habits9.ui.createyesnohabit  DarkBackground 'com.example.habits9.ui.createyesnohabit  DividerColor 'com.example.habits9.ui.createyesnohabit  ExperimentalMaterial3Api 'com.example.habits9.ui.createyesnohabit  
FontWeight 'com.example.habits9.ui.createyesnohabit  FrequencyOption 'com.example.habits9.ui.createyesnohabit  Icon 'com.example.habits9.ui.createyesnohabit  
IconButton 'com.example.habits9.ui.createyesnohabit  Icons 'com.example.habits9.ui.createyesnohabit  Modifier 'com.example.habits9.ui.createyesnohabit  OptIn 'com.example.habits9.ui.createyesnohabit  OutlinedTextField 'com.example.habits9.ui.createyesnohabit  OutlinedTextFieldDefaults 'com.example.habits9.ui.createyesnohabit  RadioButton 'com.example.habits9.ui.createyesnohabit  RadioButtonDefaults 'com.example.habits9.ui.createyesnohabit  Row 'com.example.habits9.ui.createyesnohabit  Scaffold 'com.example.habits9.ui.createyesnohabit  Spacer 'com.example.habits9.ui.createyesnohabit  String 'com.example.habits9.ui.createyesnohabit  SurfaceVariantDark 'com.example.habits9.ui.createyesnohabit  Text 'com.example.habits9.ui.createyesnohabit  
TextButton 'com.example.habits9.ui.createyesnohabit  TextPrimary 'com.example.habits9.ui.createyesnohabit  
TextSecondary 'com.example.habits9.ui.createyesnohabit  	TopAppBar 'com.example.habits9.ui.createyesnohabit  TopAppBarDefaults 'com.example.habits9.ui.createyesnohabit  Unit 'com.example.habits9.ui.createyesnohabit  
background 'com.example.habits9.ui.createyesnohabit  	clickable 'com.example.habits9.ui.createyesnohabit  colors 'com.example.habits9.ui.createyesnohabit  fillMaxSize 'com.example.habits9.ui.createyesnohabit  fillMaxWidth 'com.example.habits9.ui.createyesnohabit  getValue 'com.example.habits9.ui.createyesnohabit  height 'com.example.habits9.ui.createyesnohabit  mutableStateOf 'com.example.habits9.ui.createyesnohabit  padding 'com.example.habits9.ui.createyesnohabit  provideDelegate 'com.example.habits9.ui.createyesnohabit  remember 'com.example.habits9.ui.createyesnohabit  setValue 'com.example.habits9.ui.createyesnohabit  size 'com.example.habits9.ui.createyesnohabit  topAppBarColors 'com.example.habits9.ui.createyesnohabit  weight 'com.example.habits9.ui.createyesnohabit  width 'com.example.habits9.ui.createyesnohabit  
AccentPrimary com.example.habits9.ui.details  	Alignment com.example.habits9.ui.details  Arrangement com.example.habits9.ui.details  Build com.example.habits9.ui.details  CircularProgressIndicator com.example.habits9.ui.details  
Composable com.example.habits9.ui.details  DarkBackground com.example.habits9.ui.details  DividerColor com.example.habits9.ui.details  ExperimentalMaterial3Api com.example.habits9.ui.details  Float com.example.habits9.ui.details  
FontFamily com.example.habits9.ui.details  
FontWeight com.example.habits9.ui.details  HabitDetailsScreen com.example.habits9.ui.details  Icon com.example.habits9.ui.details  
IconButton com.example.habits9.ui.details  Icons com.example.habits9.ui.details  
MetricView com.example.habits9.ui.details  Modifier com.example.habits9.ui.details  OptIn com.example.habits9.ui.details  OverviewSection com.example.habits9.ui.details  RequiresApi com.example.habits9.ui.details  Row com.example.habits9.ui.details  Spacer com.example.habits9.ui.details  String com.example.habits9.ui.details  SubHeaderRow com.example.habits9.ui.details  SurfaceVariantDark com.example.habits9.ui.details  Text com.example.habits9.ui.details  TextPrimary com.example.habits9.ui.details  
TextSecondary com.example.habits9.ui.details  TopAppBarDefaults com.example.habits9.ui.details  Unit com.example.habits9.ui.details  fillMaxSize com.example.habits9.ui.details  fillMaxWidth com.example.habits9.ui.details  height com.example.habits9.ui.details  size com.example.habits9.ui.details  topAppBarColors com.example.habits9.ui.details  width com.example.habits9.ui.details  
AccentPrimary )com.example.habits9.ui.habittypeselection  	Alignment )com.example.habits9.ui.habittypeselection  Arrangement )com.example.habits9.ui.habittypeselection  Card )com.example.habits9.ui.habittypeselection  CardDefaults )com.example.habits9.ui.habittypeselection  Column )com.example.habits9.ui.habittypeselection  
Composable )com.example.habits9.ui.habittypeselection  DarkBackground )com.example.habits9.ui.habittypeselection  ExperimentalMaterial3Api )com.example.habits9.ui.habittypeselection  
FontWeight )com.example.habits9.ui.habittypeselection  HabitTypeSelectionScreen )com.example.habits9.ui.habittypeselection  Icon )com.example.habits9.ui.habittypeselection  
IconButton )com.example.habits9.ui.habittypeselection  Icons )com.example.habits9.ui.habittypeselection  Modifier )com.example.habits9.ui.habittypeselection  OptIn )com.example.habits9.ui.habittypeselection  RoundedCornerShape )com.example.habits9.ui.habittypeselection  Scaffold )com.example.habits9.ui.habittypeselection  Spacer )com.example.habits9.ui.habittypeselection  SurfaceVariantDark )com.example.habits9.ui.habittypeselection  Text )com.example.habits9.ui.habittypeselection  TextPrimary )com.example.habits9.ui.habittypeselection  
TextSecondary )com.example.habits9.ui.habittypeselection  	TopAppBar )com.example.habits9.ui.habittypeselection  TopAppBarDefaults )com.example.habits9.ui.habittypeselection  Unit )com.example.habits9.ui.habittypeselection  
cardColors )com.example.habits9.ui.habittypeselection  	clickable )com.example.habits9.ui.habittypeselection  fillMaxSize )com.example.habits9.ui.habittypeselection  fillMaxWidth )com.example.habits9.ui.habittypeselection  height )com.example.habits9.ui.habittypeselection  padding )com.example.habits9.ui.habittypeselection  topAppBarColors )com.example.habits9.ui.habittypeselection  
AccentPrimary com.example.habits9.ui.home  	Alignment com.example.habits9.ui.home  AllHabitsDropdown com.example.habits9.ui.home  Arrangement com.example.habits9.ui.home  Boolean com.example.habits9.ui.home  Box com.example.habits9.ui.home  Build com.example.habits9.ui.home  CircleShape com.example.habits9.ui.home  Column com.example.habits9.ui.home  CompletionIndicator com.example.habits9.ui.home  
Composable com.example.habits9.ui.home  CustomHeader com.example.habits9.ui.home  DarkBackground com.example.habits9.ui.home  DateTimeFormatter com.example.habits9.ui.home  	DayOfWeek com.example.habits9.ui.home  DividerColor com.example.habits9.ui.home  DropdownMenu com.example.habits9.ui.home  DropdownMenuItem com.example.habits9.ui.home  	Exception com.example.habits9.ui.home  ExperimentalMaterial3Api com.example.habits9.ui.home  
FontFamily com.example.habits9.ui.home  
FontWeight com.example.habits9.ui.home  Habit com.example.habits9.ui.home  HabitGridRow com.example.habits9.ui.home  HabitGridWithData com.example.habits9.ui.home  HabitRepository com.example.habits9.ui.home  
HiltViewModel com.example.habits9.ui.home  
HomeScreen com.example.habits9.ui.home  HomeUiState com.example.habits9.ui.home  
HomeViewModel com.example.habits9.ui.home  Icon com.example.habits9.ui.home  
IconButton com.example.habits9.ui.home  Icons com.example.habits9.ui.home  Inject com.example.habits9.ui.home  List com.example.habits9.ui.home  	LocalDate com.example.habits9.ui.home  Long com.example.habits9.ui.home  
MainViewModel com.example.habits9.ui.home  Map com.example.habits9.ui.home  Modifier com.example.habits9.ui.home  OptIn com.example.habits9.ui.home  RequiresApi com.example.habits9.ui.home  Row com.example.habits9.ui.home  SharingStarted com.example.habits9.ui.home  Spacer com.example.habits9.ui.home  	StateFlow com.example.habits9.ui.home  String com.example.habits9.ui.home  Stroke com.example.habits9.ui.home  SurfaceVariantDark com.example.habits9.ui.home  TemporalAdjusters com.example.habits9.ui.home  Text com.example.habits9.ui.home  	TextAlign com.example.habits9.ui.home  TextPrimary com.example.habits9.ui.home  
TextSecondary com.example.habits9.ui.home  	TypeToken com.example.habits9.ui.home  Unit com.example.habits9.ui.home  	ViewModel com.example.habits9.ui.home  WhileSubscribed com.example.habits9.ui.home  androidx com.example.habits9.ui.home  
background com.example.habits9.ui.home  
cardColors com.example.habits9.ui.home  com com.example.habits9.ui.home  	emptyList com.example.habits9.ui.home  emptyMap com.example.habits9.ui.home  fillMaxSize com.example.habits9.ui.home  fillMaxWidth com.example.habits9.ui.home  forEach com.example.habits9.ui.home  height com.example.habits9.ui.home  listOf com.example.habits9.ui.home  map com.example.habits9.ui.home  mapKeys com.example.habits9.ui.home  padding com.example.habits9.ui.home  parseCompletionDates com.example.habits9.ui.home  provideDelegate com.example.habits9.ui.home  size com.example.habits9.ui.home  spacedBy com.example.habits9.ui.home  split com.example.habits9.ui.home  stateIn com.example.habits9.ui.home  	uppercase com.example.habits9.ui.home  weight com.example.habits9.ui.home  	isLoading 'com.example.habits9.ui.home.HomeUiState  HomeUiState )com.example.habits9.ui.home.HomeViewModel  SharingStarted )com.example.habits9.ui.home.HomeViewModel  WhileSubscribed )com.example.habits9.ui.home.HomeViewModel  habitRepository )com.example.habits9.ui.home.HomeViewModel  map )com.example.habits9.ui.home.HomeViewModel  stateIn )com.example.habits9.ui.home.HomeViewModel  uiState )com.example.habits9.ui.home.HomeViewModel  viewModelScope )com.example.habits9.ui.home.HomeViewModel  example com.example.habits9.ui.home.com  habits9 'com.example.habits9.ui.home.com.example  data /com.example.habits9.ui.home.com.example.habits9  Habit 4com.example.habits9.ui.home.com.example.habits9.data  
AccentPrimary %com.example.habits9.ui.managesections  AlertDialog %com.example.habits9.ui.managesections  	Alignment %com.example.habits9.ui.managesections  Arrangement %com.example.habits9.ui.managesections  Boolean %com.example.habits9.ui.managesections  Box %com.example.habits9.ui.managesections  Card %com.example.habits9.ui.managesections  CardDefaults %com.example.habits9.ui.managesections  CircleShape %com.example.habits9.ui.managesections  CircularProgressIndicator %com.example.habits9.ui.managesections  Color %com.example.habits9.ui.managesections  Column %com.example.habits9.ui.managesections  
Composable %com.example.habits9.ui.managesections  CreateSectionDialog %com.example.habits9.ui.managesections  DarkBackground %com.example.habits9.ui.managesections  DividerColor %com.example.habits9.ui.managesections  ExperimentalMaterial3Api %com.example.habits9.ui.managesections  
FontWeight %com.example.habits9.ui.managesections  HabitSection %com.example.habits9.ui.managesections  HabitSectionRepository %com.example.habits9.ui.managesections  
HiltViewModel %com.example.habits9.ui.managesections  Icon %com.example.habits9.ui.managesections  
IconButton %com.example.habits9.ui.managesections  Icons %com.example.habits9.ui.managesections  Inject %com.example.habits9.ui.managesections  Int %com.example.habits9.ui.managesections  
LazyColumn %com.example.habits9.ui.managesections  LazyRow %com.example.habits9.ui.managesections  List %com.example.habits9.ui.managesections  ManageSectionsScreen %com.example.habits9.ui.managesections  ManageSectionsUiState %com.example.habits9.ui.managesections  ManageSectionsViewModel %com.example.habits9.ui.managesections  Modifier %com.example.habits9.ui.managesections  MutableStateFlow %com.example.habits9.ui.managesections  OptIn %com.example.habits9.ui.managesections  OutlinedTextField %com.example.habits9.ui.managesections  OutlinedTextFieldDefaults %com.example.habits9.ui.managesections  
PaddingValues %com.example.habits9.ui.managesections  Row %com.example.habits9.ui.managesections  Scaffold %com.example.habits9.ui.managesections  
SectionColors %com.example.habits9.ui.managesections  SectionListItem %com.example.habits9.ui.managesections  Spacer %com.example.habits9.ui.managesections  	StateFlow %com.example.habits9.ui.managesections  String %com.example.habits9.ui.managesections  SurfaceVariantDark %com.example.habits9.ui.managesections  Text %com.example.habits9.ui.managesections  
TextButton %com.example.habits9.ui.managesections  TextPrimary %com.example.habits9.ui.managesections  
TextSecondary %com.example.habits9.ui.managesections  	TopAppBar %com.example.habits9.ui.managesections  TopAppBarDefaults %com.example.habits9.ui.managesections  Unit %com.example.habits9.ui.managesections  	ViewModel %com.example.habits9.ui.managesections  _uiState %com.example.habits9.ui.managesections  align %com.example.habits9.ui.managesections  asStateFlow %com.example.habits9.ui.managesections  
background %com.example.habits9.ui.managesections  
cardColors %com.example.habits9.ui.managesections  	clickable %com.example.habits9.ui.managesections  collectAsState %com.example.habits9.ui.managesections  colors %com.example.habits9.ui.managesections  	emptyList %com.example.habits9.ui.managesections  fillMaxSize %com.example.habits9.ui.managesections  fillMaxWidth %com.example.habits9.ui.managesections  getValue %com.example.habits9.ui.managesections  habitSectionRepository %com.example.habits9.ui.managesections  height %com.example.habits9.ui.managesections  hideCreateDialog %com.example.habits9.ui.managesections  isBlank %com.example.habits9.ui.managesections  
isNotBlank %com.example.habits9.ui.managesections  launch %com.example.habits9.ui.managesections  listOf %com.example.habits9.ui.managesections  mutableStateOf %com.example.habits9.ui.managesections  padding %com.example.habits9.ui.managesections  provideDelegate %com.example.habits9.ui.managesections  remember %com.example.habits9.ui.managesections  setValue %com.example.habits9.ui.managesections  size %com.example.habits9.ui.managesections  spacedBy %com.example.habits9.ui.managesections  topAppBarColors %com.example.habits9.ui.managesections  trim %com.example.habits9.ui.managesections  width %com.example.habits9.ui.managesections  copy ;com.example.habits9.ui.managesections.ManageSectionsUiState  	isLoading ;com.example.habits9.ui.managesections.ManageSectionsUiState  sections ;com.example.habits9.ui.managesections.ManageSectionsUiState  showCreateDialog ;com.example.habits9.ui.managesections.ManageSectionsUiState  HabitSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  ManageSectionsUiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  MutableStateFlow =com.example.habits9.ui.managesections.ManageSectionsViewModel  _uiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  asStateFlow =com.example.habits9.ui.managesections.ManageSectionsViewModel  
createSection =com.example.habits9.ui.managesections.ManageSectionsViewModel  habitSectionRepository =com.example.habits9.ui.managesections.ManageSectionsViewModel  hideCreateDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  isBlank =com.example.habits9.ui.managesections.ManageSectionsViewModel  launch =com.example.habits9.ui.managesections.ManageSectionsViewModel  loadSections =com.example.habits9.ui.managesections.ManageSectionsViewModel  showCreateDialog =com.example.habits9.ui.managesections.ManageSectionsViewModel  trim =com.example.habits9.ui.managesections.ManageSectionsViewModel  uiState =com.example.habits9.ui.managesections.ManageSectionsViewModel  viewModelScope =com.example.habits9.ui.managesections.ManageSectionsViewModel  AndroidEntryPoint com.example.uhabits_99  Build com.example.uhabits_99  Bundle com.example.uhabits_99  ComponentActivity com.example.uhabits_99  CreateMeasurableHabitScreen com.example.uhabits_99  CreateYesNoHabitScreen com.example.uhabits_99  HabitDetailsScreen com.example.uhabits_99  HabitTypeSelectionScreen com.example.uhabits_99  
HomeScreen com.example.uhabits_99  MainActivity com.example.uhabits_99  ManageSectionsScreen com.example.uhabits_99  NavHost com.example.uhabits_99  RequiresApi com.example.uhabits_99  UHabits_99Theme com.example.uhabits_99  rememberNavController com.example.uhabits_99  Build #com.example.uhabits_99.MainActivity  CreateMeasurableHabitScreen #com.example.uhabits_99.MainActivity  CreateYesNoHabitScreen #com.example.uhabits_99.MainActivity  HabitDetailsScreen #com.example.uhabits_99.MainActivity  HabitTypeSelectionScreen #com.example.uhabits_99.MainActivity  
HomeScreen #com.example.uhabits_99.MainActivity  ManageSectionsScreen #com.example.uhabits_99.MainActivity  NavHost #com.example.uhabits_99.MainActivity  UHabits_99Theme #com.example.uhabits_99.MainActivity  
composable #com.example.uhabits_99.MainActivity  rememberNavController #com.example.uhabits_99.MainActivity  
setContent #com.example.uhabits_99.MainActivity  Boolean com.example.uhabits_99.ui.theme  Build com.example.uhabits_99.ui.theme  
Composable com.example.uhabits_99.ui.theme  DarkColorScheme com.example.uhabits_99.ui.theme  
FontFamily com.example.uhabits_99.ui.theme  
FontWeight com.example.uhabits_99.ui.theme  LightColorScheme com.example.uhabits_99.ui.theme  Pink40 com.example.uhabits_99.ui.theme  Pink80 com.example.uhabits_99.ui.theme  Purple40 com.example.uhabits_99.ui.theme  Purple80 com.example.uhabits_99.ui.theme  PurpleGrey40 com.example.uhabits_99.ui.theme  PurpleGrey80 com.example.uhabits_99.ui.theme  
Typography com.example.uhabits_99.ui.theme  UHabits_99Theme com.example.uhabits_99.ui.theme  Unit com.example.uhabits_99.ui.theme  Gson com.google.gson  fromJson com.google.gson.Gson  	TypeToken com.google.gson.reflect  type !com.google.gson.reflect.TypeToken  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  Class 	java.lang  IllegalStateException 	java.lang  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  	DayOfWeek 	java.time  	LocalDate 	java.time  MONDAY java.time.DayOfWeek  THURSDAY java.time.DayOfWeek  TUESDAY java.time.DayOfWeek  	WEDNESDAY java.time.DayOfWeek  format java.time.LocalDate  now java.time.LocalDate  parse java.time.LocalDate  with java.time.LocalDate  DateTimeFormatter java.time.format  	ofPattern "java.time.format.DateTimeFormatter  TemporalAdjusters java.time.temporal  previousOrSame $java.time.temporal.TemporalAdjusters  UUID 	java.util  
randomUUID java.util.UUID  toString java.util.UUID  Inject javax.inject  	Singleton javax.inject  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  Enum kotlin  	Exception kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  
ShortArray kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  plus kotlin  not kotlin.Boolean  sp 
kotlin.Double  AT_LEAST kotlin.Enum  AT_MOST kotlin.Enum  	Companion kotlin.Enum  	HabitType kotlin.Enum  IllegalStateException kotlin.Enum  Int kotlin.Enum  	NUMERICAL kotlin.Enum  NumericalHabitType kotlin.Enum  YES_NO kotlin.Enum  AT_LEAST kotlin.Enum.Companion  AT_MOST kotlin.Enum.Companion  IllegalStateException kotlin.Enum.Companion  	NUMERICAL kotlin.Enum.Companion  YES_NO kotlin.Enum.Companion  div kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  	compareTo 
kotlin.Int  isBlank 
kotlin.String  
isNotBlank 
kotlin.String  replace 
kotlin.String  split 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  toInt kotlin.ULong  List kotlin.collections  Map kotlin.collections  Set kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  forEach kotlin.collections  listOf kotlin.collections  mapKeys kotlin.collections  plus kotlin.collections  get kotlin.collections.List  isEmpty kotlin.collections.List  plus kotlin.collections.List  size kotlin.collections.List  Entry kotlin.collections.Map  mapKeys kotlin.collections.Map  key kotlin.collections.Map.Entry  SuspendFunction1 kotlin.coroutines  java 
kotlin.jvm  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  forEach kotlin.sequences  plus kotlin.sequences  forEach kotlin.text  isBlank kotlin.text  
isNotBlank kotlin.text  plus kotlin.text  replace kotlin.text  split kotlin.text  trim kotlin.text  
trimIndent kotlin.text  	uppercase kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  HabitSection !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  habitRepository !kotlinx.coroutines.CoroutineScope  habitSectionRepository !kotlinx.coroutines.CoroutineScope  hideCreateDialog !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  trim !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  map kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  	Companion &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  collectAsState !kotlinx.coroutines.flow.StateFlow                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     