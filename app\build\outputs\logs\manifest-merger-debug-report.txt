-- Merging decision tree log ---
manifest
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:2:1-28:12
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:2:1-28:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8577a76b53f87fe86dddbd45c2bc52fd\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e432ada87e710d86ed186f305e053b3\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33676ca374f0b104094c9419b56490\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4758b6a1529964abd5a0fd9f125d1ae\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3257b064c5ae6c368a5c466c5fe843f\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\24b49e2a4049140dbbfac57bb961443d\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a38caaed26953f68d4e4dcd811be4b54\transformed\navigation-compose-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcffc63db0d22c143bda016d1f78d69c\transformed\hilt-android-2.49\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27e64a804dd4e599621d9aba1165eced\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd31b0e27cb609ea82d5fa835a02a3b2\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\388215e052bfbccf4f94cb84c9cefc6e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b23caab46f5059a39140b07fa3aa110a\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2cb99a95f887570ba093d642415a010\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\631a5553b29e2f5c4134fe90739584c4\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\4549c3e0edb3787e2a22faf48871ae68\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\21e75489b7ffa2d58314b676b69aee33\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6310b3a66b9ae96d2a719088ffb539d\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0884c976848e5dd552ed3080e9a85b7d\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\617613d6e879def2a21d905cc9c3691b\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3271631b0c5277608b79e8d764824f9e\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd1a3482f9d7506ac2395b2785f587a8\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c1977b6605a15b91b19444fd768d8b3\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3e4ea894d48af878c6c504a1b5169f\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\aba80cdd83a1168020e9c5410fceaacd\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\13400e8269a264623b5857a8d7b97faf\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\42a931ffcc5ee8331458976770d64c02\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae7e679f89bc128fdc30b84832b8d0ca\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\4aae798abe3e109e28b3f9ff288c6e8f\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0bc29973724a360ec1ff1aeb00f3a003\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\371487a2110f49d09a4c0ec1721c46cd\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2894c3a0c62b4263a01cc5286243263\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef7f40825c7ec01ad09ccdc824f27173\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\52b2887596def3f6f3b793e84c1184ca\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81e600a4feba0691c2e292176f340800\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9567af03564c4840edeb2a0c41e1939a\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fba119813bc47109b61eb775bef255\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\139230547b0930090775332b03b2225f\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4645fe27799dedf79fa77974ddb41865\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\271b47278c9a188b0aa9e220e9ea5546\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b28c11925b3e38d97dc01380e58ca0c\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c77007972efb453e42a08a03fe2d6567\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\56a1fafb0643e06164cf013100303487\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d93bc719be3146f754ab36d070e6dc4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\286ba33d943ad400560efffaf0d69dc5\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96120259c05f5da6ba9822d339bd51d3\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f61e9079e8cd071a21a27e646cdb82c\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f42d6341bf78ae30cc94475e1e3f0d\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa574699e44c3ddb5ce6bfc5153510e\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7df2d9bb860c696f946d801bb9c63e21\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\997709f82ae8917c0d6d085f5ad5abc6\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\216ef6bbf25fb191e6ef7c4fe6b8a3bc\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ccefebee63e5b96ec25228005fb69d58\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1890490f07b01a47f508ecf990e650b6\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7288b921a6e4ead3c0d4c7216fb313d\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f27048be1fd6508b83ac081cbd1ddd\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb0eca77f0855e4510a66628a72df918\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cab2dde8a1a02882fd2c0bce9979920\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd10aa85cfb9a6ef05224470eba6e23\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78313f7f8de54defee40d109a2b7f5a1\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e68edd130f75e006eff6d16735380c2e\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b4bcad4b1128e1f5367a9a7f46103f8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88065ebf7b14eec554b5affa9b2b2019\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9df594503700d7bf860b01ad69a9aac\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7256872cab947b38ec8970bbd75c3d23\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3065fbb3633f97d876c545a05472997\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\535914b828c4295c6701b82b6a7f8b9c\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:5:5-26:19
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:5:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6310b3a66b9ae96d2a719088ffb539d\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6310b3a66b9ae96d2a719088ffb539d\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\371487a2110f49d09a4c0ec1721c46cd\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\371487a2110f49d09a4c0ec1721c46cd\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f42d6341bf78ae30cc94475e1e3f0d\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f42d6341bf78ae30cc94475e1e3f0d\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1890490f07b01a47f508ecf990e650b6\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1890490f07b01a47f508ecf990e650b6\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b4bcad4b1128e1f5367a9a7f46103f8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b4bcad4b1128e1f5367a9a7f46103f8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7256872cab947b38ec8970bbd75c3d23\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7256872cab947b38ec8970bbd75c3d23\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:13:9-35
	android:label
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:11:9-41
	android:fullBackupContent
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:9:9-54
	android:roundIcon
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:12:9-54
	android:icon
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:10:9-43
	android:allowBackup
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:7:9-35
	android:theme
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:14:9-48
	android:dataExtractionRules
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:8:9-65
	android:name
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:6:9-61
activity#com.example.uhabits_99.MainActivity
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:15:9-25:20
	android:label
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:18:13-45
	android:exported
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:17:13-36
	android:theme
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:19:13-52
	android:name
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:20:13-24:29
action#android.intent.action.MAIN
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:23:17-77
	android:name
		ADDED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml:23:27-74
uses-sdk
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8577a76b53f87fe86dddbd45c2bc52fd\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8577a76b53f87fe86dddbd45c2bc52fd\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e432ada87e710d86ed186f305e053b3\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e432ada87e710d86ed186f305e053b3\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33676ca374f0b104094c9419b56490\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a33676ca374f0b104094c9419b56490\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4758b6a1529964abd5a0fd9f125d1ae\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4758b6a1529964abd5a0fd9f125d1ae\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3257b064c5ae6c368a5c466c5fe843f\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\c3257b064c5ae6c368a5c466c5fe843f\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\24b49e2a4049140dbbfac57bb961443d\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\24b49e2a4049140dbbfac57bb961443d\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a38caaed26953f68d4e4dcd811be4b54\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\a38caaed26953f68d4e4dcd811be4b54\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcffc63db0d22c143bda016d1f78d69c\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcffc63db0d22c143bda016d1f78d69c\transformed\hilt-android-2.49\AndroidManifest.xml:18:3-42
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27e64a804dd4e599621d9aba1165eced\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\27e64a804dd4e599621d9aba1165eced\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd31b0e27cb609ea82d5fa835a02a3b2\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd31b0e27cb609ea82d5fa835a02a3b2\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\388215e052bfbccf4f94cb84c9cefc6e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\388215e052bfbccf4f94cb84c9cefc6e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b23caab46f5059a39140b07fa3aa110a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b23caab46f5059a39140b07fa3aa110a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2cb99a95f887570ba093d642415a010\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2cb99a95f887570ba093d642415a010\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\631a5553b29e2f5c4134fe90739584c4\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\631a5553b29e2f5c4134fe90739584c4\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\4549c3e0edb3787e2a22faf48871ae68\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\4549c3e0edb3787e2a22faf48871ae68\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\21e75489b7ffa2d58314b676b69aee33\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\21e75489b7ffa2d58314b676b69aee33\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6310b3a66b9ae96d2a719088ffb539d\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6310b3a66b9ae96d2a719088ffb539d\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0884c976848e5dd552ed3080e9a85b7d\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0884c976848e5dd552ed3080e9a85b7d\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\617613d6e879def2a21d905cc9c3691b\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\617613d6e879def2a21d905cc9c3691b\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3271631b0c5277608b79e8d764824f9e\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3271631b0c5277608b79e8d764824f9e\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd1a3482f9d7506ac2395b2785f587a8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd1a3482f9d7506ac2395b2785f587a8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c1977b6605a15b91b19444fd768d8b3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c1977b6605a15b91b19444fd768d8b3\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3e4ea894d48af878c6c504a1b5169f\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec3e4ea894d48af878c6c504a1b5169f\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\aba80cdd83a1168020e9c5410fceaacd\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\aba80cdd83a1168020e9c5410fceaacd\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\13400e8269a264623b5857a8d7b97faf\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\13400e8269a264623b5857a8d7b97faf\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\42a931ffcc5ee8331458976770d64c02\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\42a931ffcc5ee8331458976770d64c02\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae7e679f89bc128fdc30b84832b8d0ca\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae7e679f89bc128fdc30b84832b8d0ca\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\4aae798abe3e109e28b3f9ff288c6e8f\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\4aae798abe3e109e28b3f9ff288c6e8f\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0bc29973724a360ec1ff1aeb00f3a003\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0bc29973724a360ec1ff1aeb00f3a003\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\371487a2110f49d09a4c0ec1721c46cd\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\371487a2110f49d09a4c0ec1721c46cd\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2894c3a0c62b4263a01cc5286243263\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2894c3a0c62b4263a01cc5286243263\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef7f40825c7ec01ad09ccdc824f27173\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef7f40825c7ec01ad09ccdc824f27173\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\52b2887596def3f6f3b793e84c1184ca\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\52b2887596def3f6f3b793e84c1184ca\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81e600a4feba0691c2e292176f340800\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81e600a4feba0691c2e292176f340800\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9567af03564c4840edeb2a0c41e1939a\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9567af03564c4840edeb2a0c41e1939a\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fba119813bc47109b61eb775bef255\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58fba119813bc47109b61eb775bef255\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\139230547b0930090775332b03b2225f\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\139230547b0930090775332b03b2225f\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4645fe27799dedf79fa77974ddb41865\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4645fe27799dedf79fa77974ddb41865\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\271b47278c9a188b0aa9e220e9ea5546\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\271b47278c9a188b0aa9e220e9ea5546\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b28c11925b3e38d97dc01380e58ca0c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b28c11925b3e38d97dc01380e58ca0c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c77007972efb453e42a08a03fe2d6567\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c77007972efb453e42a08a03fe2d6567\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\56a1fafb0643e06164cf013100303487\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\56a1fafb0643e06164cf013100303487\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d93bc719be3146f754ab36d070e6dc4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d93bc719be3146f754ab36d070e6dc4\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\286ba33d943ad400560efffaf0d69dc5\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\286ba33d943ad400560efffaf0d69dc5\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96120259c05f5da6ba9822d339bd51d3\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\96120259c05f5da6ba9822d339bd51d3\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f61e9079e8cd071a21a27e646cdb82c\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f61e9079e8cd071a21a27e646cdb82c\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f42d6341bf78ae30cc94475e1e3f0d\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f42d6341bf78ae30cc94475e1e3f0d\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa574699e44c3ddb5ce6bfc5153510e\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0aa574699e44c3ddb5ce6bfc5153510e\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7df2d9bb860c696f946d801bb9c63e21\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7df2d9bb860c696f946d801bb9c63e21\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\997709f82ae8917c0d6d085f5ad5abc6\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\997709f82ae8917c0d6d085f5ad5abc6\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\216ef6bbf25fb191e6ef7c4fe6b8a3bc\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\216ef6bbf25fb191e6ef7c4fe6b8a3bc\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ccefebee63e5b96ec25228005fb69d58\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ccefebee63e5b96ec25228005fb69d58\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1890490f07b01a47f508ecf990e650b6\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1890490f07b01a47f508ecf990e650b6\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7288b921a6e4ead3c0d4c7216fb313d\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a7288b921a6e4ead3c0d4c7216fb313d\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f27048be1fd6508b83ac081cbd1ddd\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e6f27048be1fd6508b83ac081cbd1ddd\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb0eca77f0855e4510a66628a72df918\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb0eca77f0855e4510a66628a72df918\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cab2dde8a1a02882fd2c0bce9979920\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\7cab2dde8a1a02882fd2c0bce9979920\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd10aa85cfb9a6ef05224470eba6e23\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cd10aa85cfb9a6ef05224470eba6e23\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78313f7f8de54defee40d109a2b7f5a1\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\78313f7f8de54defee40d109a2b7f5a1\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e68edd130f75e006eff6d16735380c2e\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e68edd130f75e006eff6d16735380c2e\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b4bcad4b1128e1f5367a9a7f46103f8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b4bcad4b1128e1f5367a9a7f46103f8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88065ebf7b14eec554b5affa9b2b2019\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88065ebf7b14eec554b5affa9b2b2019\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9df594503700d7bf860b01ad69a9aac\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9df594503700d7bf860b01ad69a9aac\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7256872cab947b38ec8970bbd75c3d23\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7256872cab947b38ec8970bbd75c3d23\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3065fbb3633f97d876c545a05472997\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3065fbb3633f97d876c545a05472997\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\535914b828c4295c6701b82b6a7f8b9c\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.49] C:\Users\<USER>\.gradle\caches\8.13\transforms\535914b828c4295c6701b82b6a7f8b9c\transformed\dagger-lint-aar-2.49\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\Habit_Tracker_WorkSpace\UHabits_99\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6310b3a66b9ae96d2a719088ffb539d\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6310b3a66b9ae96d2a719088ffb539d\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6310b3a66b9ae96d2a719088ffb539d\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\371487a2110f49d09a4c0ec1721c46cd\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\371487a2110f49d09a4c0ec1721c46cd\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7256872cab947b38ec8970bbd75c3d23\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7256872cab947b38ec8970bbd75c3d23\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8c4964c991a2e4e9de83daaca0a6b7\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.uhabits_99.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.uhabits_99.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac993e4416ee2ee877a3e9f255a847e6\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\371487a2110f49d09a4c0ec1721c46cd\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\371487a2110f49d09a4c0ec1721c46cd\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\371487a2110f49d09a4c0ec1721c46cd\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f42d6341bf78ae30cc94475e1e3f0d\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f42d6341bf78ae30cc94475e1e3f0d\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f42d6341bf78ae30cc94475e1e3f0d\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1890490f07b01a47f508ecf990e650b6\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1890490f07b01a47f508ecf990e650b6\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1890490f07b01a47f508ecf990e650b6\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1890490f07b01a47f508ecf990e650b6\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1890490f07b01a47f508ecf990e650b6\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b3ed93aa5610a58ecae0fe78a6f4ca2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
