package com.example.habits9.ui.home

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.Card
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.habits9.ui.MainViewModel
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.TemporalAdjusters

// Design System Colors - Dark Theme
val DarkBackground = Color(0xFF121826) // background
val SurfaceVariantDark = Color(0xFF1A202C) // surface-variant
val TextPrimary = Color(0xFFE2E8F0) // text-primary
val TextSecondary = Color(0xFFA0AEC0) // text-secondary
val AccentPrimary = Color(0xFF81E6D9) // accent-primary
val DividerColor = Color(0xFF2D3748) // divider

@RequiresApi(Build.VERSION_CODES.O)
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    homeViewModel: HomeViewModel = hiltViewModel(),
    mainViewModel: MainViewModel = hiltViewModel(),
    onHabitClick: (Long) -> Unit = {},
    onCreateHabitClick: () -> Unit = {},
    onManageSectionsClick: () -> Unit = {}
) {
    val homeUiState by homeViewModel.uiState.collectAsState()
    val mainUiState by mainViewModel.uiState.collectAsState()

    val today = LocalDate.now()
    val formatterDayOfMonth = DateTimeFormatter.ofPattern("dd")
    val formatterDayOfWeek = DateTimeFormatter.ofPattern("EE")

    val thursday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.THURSDAY))
    val wednesday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.WEDNESDAY))
    val tuesday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.TUESDAY))
    val monday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))

    Scaffold(
        containerColor = DarkBackground // Set scaffold background color
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Custom Header with Add Habit functionality
            CustomHeader(
                onCreateHabitClick = onCreateHabitClick,
                onManageSectionsClick = onManageSectionsClick
            )
            
            // Content with horizontal padding
            Column(
                modifier = Modifier.padding(horizontal = 16.dp)
            ) {
                if (homeUiState.isLoading) {
                    Text("Loading habits...", modifier = Modifier.padding(top = 16.dp), color = TextPrimary)
                } else {
                    // Always show the dynamic habit grid connected to MainViewModel
                    HabitGridWithData(
                        habits = mainUiState.habits,
                        thursdayDate = "${thursday.format(formatterDayOfMonth)}\n${thursday.format(formatterDayOfWeek).uppercase()}",
                        wednesdayDate = "${wednesday.format(formatterDayOfMonth)}\n${wednesday.format(formatterDayOfWeek).uppercase()}",
                        tuesdayDate = "${tuesday.format(formatterDayOfMonth)}\n${tuesday.format(formatterDayOfWeek).uppercase()}",
                        mondayDate = "${monday.format(formatterDayOfMonth)}\n${monday.format(formatterDayOfWeek).uppercase()}",
                        onHabitClick = onHabitClick
                    )
                }
            }
        }
        
    }
}

@Composable
fun CustomHeader(
    onCreateHabitClick: () -> Unit = {},
    onManageSectionsClick: () -> Unit = {}
) {
    var showDropdownMenu by remember { mutableStateOf(false) }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(SurfaceVariantDark)
            .padding(horizontal = 20.dp, vertical = 16.dp) // 20px horizontal padding as specified
    ) {
        // First Row (Top Line): "This Week: 60%" and "W29" + menu icon
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Left Content: "This Week: 60%" with different colors
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = "This Week: ",
                    color = TextPrimary,
                    fontSize = 14.sp,
                    fontFamily = FontFamily.Default,
                    fontWeight = FontWeight.SemiBold // 600 weight
                )
                Text(
                    text = "60%",
                    color = AccentPrimary,
                    fontSize = 14.sp,
                    fontFamily = FontFamily.Default,
                    fontWeight = FontWeight.SemiBold // 600 weight
                )
            }
            
            // Right Content: W29 and menu icon
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(16.dp) // 16px spacing between elements
            ) {
                Text(
                    text = "W29",
                    color = TextPrimary,
                    fontSize = 14.sp,
                    fontFamily = FontFamily.Default,
                    fontWeight = FontWeight.SemiBold // 600 weight
                )
                Box {
                    IconButton(
                        onClick = { showDropdownMenu = true },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.MoreVert,
                            contentDescription = "More Options",
                            tint = TextPrimary,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    
                    DropdownMenu(
                        expanded = showDropdownMenu,
                        onDismissRequest = { showDropdownMenu = false },
                        modifier = Modifier.background(SurfaceVariantDark)
                    ) {
                        DropdownMenuItem(
                            text = { 
                                Text(
                                    text = "Manage Sections",
                                    color = TextPrimary,
                                    fontSize = 14.sp
                                )
                            },
                            onClick = {
                                showDropdownMenu = false
                                onManageSectionsClick()
                            }
                        )
                    }
                }
            }
        }
        
        // Vertical Spacing: 16px between rows
        Spacer(modifier = Modifier.height(16.dp))
        
        // Second Row (Bottom Line): "All Habits" pill and add icon
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Left Content: "All Habits" pill-shaped dropdown
            AllHabitsDropdown()
            
            // Right Content: Add icon (clickable)
            IconButton(
                onClick = onCreateHabitClick,
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Add Habit",
                    tint = TextPrimary,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}

@Composable
fun AllHabitsDropdown() {
    Card(
        modifier = Modifier,
        shape = RoundedCornerShape(50), // Fully rounded corners for pill shape
        colors = androidx.compose.material3.CardDefaults.cardColors(
            containerColor = SurfaceVariantDark
        )
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp), // Internal padding
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp) // 8px gap between elements
        ) {
            // Status dot (8px circle)
            Box(
                modifier = Modifier
                    .size(8.dp)
                    .background(
                        color = TextSecondary,
                        shape = CircleShape
                    )
            )
            
            // "All Habits" text
            Text(
                text = "All Habits",
                color = TextPrimary,
                fontSize = 12.sp,
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Normal // 400 weight
            )
            
            // Chevron icon
            Icon(
                imageVector = Icons.Default.KeyboardArrowDown,
                contentDescription = "Dropdown",
                tint = TextSecondary,
                modifier = Modifier.size(16.dp)
            )
        }
    }
}


@RequiresApi(Build.VERSION_CODES.O)
@Composable
fun HabitGridWithData(
    habits: List<com.example.habits9.data.Habit>,
    thursdayDate: String,
    wednesdayDate: String,
    tuesdayDate: String,
    mondayDate: String,
    onHabitClick: (Long) -> Unit = {}
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        // Header Row (same as HabitGrid)
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(SurfaceVariantDark)
                .padding(vertical = 8.dp, horizontal = 4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // Habit Name Column Header
            Text(
                text = "Habit",
                modifier = Modifier.weight(2f),
                color = TextPrimary,
                fontSize = 12.sp,
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Normal
            )
            
            // Day Headers
            listOf(thursdayDate, wednesdayDate, tuesdayDate, mondayDate).forEach { dayDate ->
                Column(
                    modifier = Modifier.weight(1f),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    val parts = dayDate.split("\n")
                    Text(
                        text = parts[1], // Day abbreviation (THU, WED, etc.)
                        color = TextSecondary,
                        fontSize = 10.sp,
                        fontFamily = FontFamily.Monospace,
                        fontWeight = FontWeight.Normal,
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = parts[0], // Date number
                        color = TextSecondary,
                        fontSize = 10.sp,
                        fontFamily = FontFamily.Monospace,
                        fontWeight = FontWeight.Normal,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
        
        // Divider
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(1.dp)
                .background(DividerColor)
        )
        
        // Display actual habits
        habits.forEach { habit ->
            HabitGridRow(
                habitName = habit.name,
                completionStatus = listOf(false, false, false, false), // Default to all pending for now
                onClick = { onHabitClick(habit.id) }
            )
        }
    }
}

@Composable
fun HabitGridRow(
    habitName: String,
    completionStatus: List<Boolean>,
    onClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 8.dp, horizontal = 4.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Habit Name
        Text(
            text = habitName,
            modifier = Modifier.weight(2f),
            color = TextPrimary,
            fontSize = 12.sp,
            fontFamily = FontFamily.Default,
            fontWeight = FontWeight.Normal
        )
        
        // Completion Indicators for each day
        completionStatus.forEach { completed ->
            Box(
                modifier = Modifier.weight(1f),
                contentAlignment = Alignment.Center
            ) {
                CompletionIndicator(completed = completed)
            }
        }
    }
}

@Composable
fun CompletionIndicator(completed: Boolean) {
    if (completed) {
        // Filled circle for completed state
        Canvas(
            modifier = Modifier.size(18.dp)
        ) {
            drawCircle(
                color = AccentPrimary,
                radius = size.minDimension / 2
            )
        }
    } else {
        // Outlined circle for pending state
        Canvas(
            modifier = Modifier.size(18.dp)
        ) {
            drawCircle(
                color = TextSecondary,
                radius = size.minDimension / 2,
                style = Stroke(width = 1.dp.toPx())
            )
        }
    }
}

// Helper function to parse completion dates from JSON
@RequiresApi(Build.VERSION_CODES.O)
private fun parseCompletionDates(json: String): Map<LocalDate, Boolean> {
    return try {
        val mapType = object : TypeToken<Map<String, Boolean>>() {}.type
        val stringMap: Map<String, Boolean> = Gson().fromJson(json, mapType) ?: emptyMap()
        // Convert string keys to LocalDate keys
        stringMap.mapKeys { LocalDate.parse(it.key) }
    } catch (e: Exception) {
        emptyMap()
    }
}