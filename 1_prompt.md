# Prompt for AI Developer: Task 14 - Implement Edit and Delete for Habit Sections (Phase 3.2)

**Objective:** Enhance the `ManageSectionsScreen` by implementing full editing and deleting functionality for habit sections. This will complete the core CRUD (Create, Read, Update, Delete) operations for this feature.

---

## 1. Mandatory Development Guidelines

Before beginning any work, you must adhere to the following principles:

1.  **Style Guide Adherence:** Before implementing any new feature, you must always refer to the style guide for any styling-related queries or decisions. This ensures consistency and adherence to the project’s visual and structural standards. The style guide is located in the **`style.md`** file in the root folder and should be considered the single source of truth for all style-related questions.
2.  **Reference Project Study:** It is essential to first study the reference project before beginning any implementation. This helps you understand how similar features or workflows have been approached previously, ensuring alignment and reducing the risk of redundant or conflicting code. The reference project can be found in the **`uhabits-dev`** folder located in the root directory.
3.  **Current Project Analysis:** Before any code is written, you must take time to understand the current project structure, especially in relation to the new feature being developed. This is mandatory. Even if the feature is entirely new, there may be existing components, utilities, or foundational logic already present in the codebase. Understanding these connections is crucial to integrating the new feature cleanly and efficiently into the existing architecture.
4.  **Post-Implementation Cleanup:** Once the implementation is complete, you must ensure that the codebase is kept clean and organized. This involves removing any unnecessary routes, folders, files, or duplicate implementations that may have been created during the development process. Maintaining a clean codebase is vital for long-term scalability and to prevent confusion for future contributors.
5.  **Clarification Protocol:** If there is any confusion or lack of clarity regarding the feature requirements or implementation approach at any stage, you **must stop immediately** and seek clarification. This should be done either by consulting with the project lead or directly with me. It is better to resolve uncertainties upfront than to proceed with incorrect assumptions, which can lead to rework and wasted effort.

---

## 2. Detailed Implementation Requirements

### Editing Functionality

1.  **Update ViewModel:** Enhance the `ManageSectionsViewModel` to handle the editing state. This includes logic to track which section is currently being edited.
2.  **Make List Items Tappable:** Modify the `SectionItem` composable on the `ManageSectionsScreen` so that the entire row is clickable.
3.  **Show Edit Dialog:** When a user taps on a section item, the **same** `AlertDialog` used for creating a new section must be displayed.
4.  **Pre-fill Dialog Data:** The dialog must be populated with the `name` and `color` of the specific section that was tapped.
5.  **Implement Update Logic:**
    * The dialog's title should change to "Edit Section".
    * The primary button text should change to "Save".
    * When the user clicks "Save", a `updateSection` function in the `ViewModel` must be called to persist the changes to the database.

### Deleting Functionality

1.  **Update Item UI:** Modify the `SectionItem` composable to include a "delete" icon (e.g., a trash can icon) on the right-hand side.
2.  **Implement Confirmation Dialog:** When the delete icon is tapped, a **new, separate** `AlertDialog` must be displayed to confirm the action.
    * **Dialog Title:** "Delete Section?"
    * **Dialog Message:** "Are you sure you want to delete '[Section Name]'? This action cannot be undone." (Dynamically display the actual section name).
    * **Dialog Buttons:** Two buttons: "Cancel" (dismisses the dialog) and "Delete" (proceeds with deletion).
3.  **Implement Delete Logic:** If the user confirms by tapping "Delete", a `deleteSection` function in the `ViewModel` must be called to remove the item from the database.

---

## 3. Out of Scope for This Task

* Reordering sections (drag-and-drop functionality) is not part of this task and will be handled in Phase 4.