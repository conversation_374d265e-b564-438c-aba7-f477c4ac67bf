  Activity android.app  Context android.content  ContextWrapper android.content  Build 
android.os  
VERSION_CODES android.os.Build  ContextThemeWrapper android.view  ComponentActivity androidx.activity  	Companion #androidx.activity.ComponentActivity  ComponentActivity androidx.core.app  	ViewModel androidx.lifecycle  OnConflictStrategy 
androidx.room  RoomDatabase 
androidx.room  	Companion  androidx.room.OnConflictStrategy  	Migration androidx.room.migration  SupportSQLiteDatabase androidx.sqlite.db  Habit com.example.habits9.data  HabitDao com.example.habits9.data  
HabitDatabase com.example.habits9.data  HabitRepository com.example.habits9.data  HabitSection com.example.habits9.data  HabitSectionDao com.example.habits9.data  HabitSectionRepository com.example.habits9.data  DatabaseModule com.example.habits9.di  <no name provided> 3com.example.habits9.di.DatabaseModule.MIGRATION_3_4  
MainViewModel com.example.habits9.ui  
HomeViewModel com.example.habits9.ui.home  ManageSectionsViewModel %com.example.habits9.ui.managesections  MainActivity com.example.uhabits_99  String kotlin                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           