package com.example.habits9.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitType
import com.example.habits9.data.NumericalHabitType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class MainUiState(
    val habits: List<Habit> = emptyList(),
    val isLoading: Boolean = false,
    val showAddHabitDialog: Boolean = false
)

@HiltViewModel
class MainViewModel @Inject constructor(
    private val habitRepository: HabitRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()

    // In-memory list for this task (as specified in prompt - no persistence yet)
    private val _habitsList = MutableStateFlow<List<Habit>>(emptyList())

    init {
        // Initialize with empty list for now
        _uiState.value = MainUiState(habits = emptyList())
    }

    fun showAddHabitDialog() {
        _uiState.value = _uiState.value.copy(showAddHabitDialog = true)
    }

    fun hideAddHabitDialog() {
        _uiState.value = _uiState.value.copy(showAddHabitDialog = false)
    }

    fun addHabit(
        name: String, 
        description: String = "", 
        habitType: HabitType = HabitType.YES_NO,
        targetValue: Double? = null,
        unit: String? = null
    ) {
        if (name.isBlank()) return

        val newHabit = Habit(
            id = System.currentTimeMillis(), // Simple ID generation for in-memory storage
            name = name.trim(),
            description = description.trim(),
            creationDate = System.currentTimeMillis(),
            position = _uiState.value.habits.size, // Add to end of list
            type = habitType.value,
            targetType = NumericalHabitType.AT_LEAST.value, // Default for numerical habits
            targetValue = targetValue ?: 0.0,
            unit = unit?.trim() ?: ""
        )

        // Add to in-memory list
        val updatedHabits = _uiState.value.habits + newHabit
        _uiState.value = _uiState.value.copy(
            habits = updatedHabits,
            showAddHabitDialog = false
        )
    }

    // For future database persistence (not implemented in this task)
    private fun addHabitToDatabase(habit: Habit) {
        viewModelScope.launch {
            habitRepository.insertHabit(habit)
        }
    }
}