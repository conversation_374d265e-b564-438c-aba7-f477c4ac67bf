We got some errors in the implementation . I’ve saved an error log in ./2_error.md. Please follow this exact structured debugging process to investigate and resolve the issue:

1. Understand the Error
        Open and thoroughly read the contents of ./2_error.md. Carefully interpret what the error says — understand the type of exception, the stack trace, and any file paths or line numbers referenced.

2. Trace the Error Location
        Navigate to all relevant files and lines mentioned in the error log. Understand the surrounding code and the full implementation of the feature where the error has occurred. Focus on control flow, input/output, and any dependent components.

3. Comprehend the Current Feature Implementation
        Develop a clear mental model of how the entire feature is structured and functions. Identify how the involved files and methods interact and what they’re intended to do.

4. Determine the Root Cause
        Before implementing any changes, it is mandatory to identify and finalize the true root cause of the issue. Think deeply — is it due to a logic error, missing config, incompatible encoding, race condition, or misused library? Clearly state the root cause once identified.

5. Cross-Reference the Reference Project
        Once the root cause is finalized, it is required to examine the reference project at ./uhabits-dev. Compare relevant parts of the implementation. Look for differences or proven approaches that could guide the solution.

6. Plan and Execute the Fix
        After gaining full understanding and validating it against the reference project, proceed to implement the fix with precision. Ensure that:
                1. The change is minimal and localized.
                2. It addresses the root cause directly.
                3. It does not introduce side effects.

7. Verify : Test the fix thoroughly. 


