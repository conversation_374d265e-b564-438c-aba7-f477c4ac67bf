package com.example.habits9.data

import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.<PERSON><PERSON>

@Singleton
class HabitRepository @Inject constructor(private val habitDao: HabitDao) {

    fun getAllHabits(): Flow<List<Habit>> = habitDao.getAllHabits()

    fun getHabitById(habitId: Long): Flow<Habit> = habitDao.getHabitById(habitId)

    suspend fun insertHabit(habit: Habit) {
        habitDao.insertHabit(habit)
    }

    suspend fun updateHabit(habit: Habit) {
        habitDao.updateHabit(habit)
    }

    suspend fun deleteHabit(habit: Habit) {
        habitDao.deleteHabit(habit)
    }
}
