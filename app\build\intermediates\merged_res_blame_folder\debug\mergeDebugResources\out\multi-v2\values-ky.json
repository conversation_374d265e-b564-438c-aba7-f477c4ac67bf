{"logs": [{"outputFile": "com.example.uhabits_99.app-mergeDebugResources-61:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ac993e4416ee2ee877a3e9f255a847e6\\transformed\\core-1.16.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,569,673,784", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "150,252,355,462,564,668,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,307,410,517,619,723,8500", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "200,302,405,512,614,718,829,8596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd31b0e27cb609ea82d5fa835a02a3b2\\transformed\\material3-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,295,409,534,634,732,847,983,1124,1280,1364,1462,1554,1651,1767,1886,1989,2125,2259,2396,2571,2700,2817,2937,3058,3151,3249,3371,3508,3611,3736,3841,3975,4114,4223,4325,4401,4500,4604,4718,4804,4889,5001,5090,5174,5274,5375,5471,5568,5655,5766,5865,5965,6113,6203,6322", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,113,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "173,290,404,529,629,727,842,978,1119,1275,1359,1457,1549,1646,1762,1881,1984,2120,2254,2391,2566,2695,2812,2932,3053,3146,3244,3366,3503,3606,3731,3836,3970,4109,4218,4320,4396,4495,4599,4713,4799,4884,4996,5085,5169,5269,5370,5466,5563,5650,5761,5860,5960,6108,6198,6317,6425"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1486,1609,1726,1840,1965,2065,2163,2278,2414,2555,2711,2795,2893,2985,3082,3198,3317,3420,3556,3690,3827,4002,4131,4248,4368,4489,4582,4680,4802,4939,5042,5167,5272,5406,5545,5654,5756,5832,5931,6035,6149,6235,6320,6432,6521,6605,6705,6806,6902,6999,7086,7197,7296,7396,7544,7634,7753", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,113,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "1604,1721,1835,1960,2060,2158,2273,2409,2550,2706,2790,2888,2980,3077,3193,3312,3415,3551,3685,3822,3997,4126,4243,4363,4484,4577,4675,4797,4934,5037,5162,5267,5401,5540,5649,5751,5827,5926,6030,6144,6230,6315,6427,6516,6600,6700,6801,6897,6994,7081,7192,7291,7391,7539,7629,7748,7856"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ec3e4ea894d48af878c6c504a1b5169f\\transformed\\foundation-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,103", "endOffsets": "149,253"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8866,8965", "endColumns": "98,103", "endOffsets": "8960,9064"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3f61e9079e8cd071a21a27e646cdb82c\\transformed\\ui-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,392,492,577,659,757,846,931,1016,1103,1176,1250,1323,1396,1475,1543", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,73,72,72,78,67,117", "endOffsets": "193,277,387,487,572,654,752,841,926,1011,1098,1171,1245,1318,1391,1470,1538,1656"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "834,927,1011,1121,1221,1306,1388,7861,7950,8035,8120,8207,8280,8354,8427,8601,8680,8748", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,73,72,72,78,67,117", "endOffsets": "922,1006,1116,1216,1301,1383,1481,7945,8030,8115,8202,8275,8349,8422,8495,8675,8743,8861"}}]}]}