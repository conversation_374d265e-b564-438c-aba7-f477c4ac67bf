package com.example.habits9.ui;

import com.example.habits9.data.HabitRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  public MainViewModel_Factory(Provider<HabitRepository> habitRepositoryProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(habitRepositoryProvider.get());
  }

  public static MainViewModel_Factory create(Provider<HabitRepository> habitRepositoryProvider) {
    return new MainViewModel_Factory(habitRepositoryProvider);
  }

  public static MainViewModel newInstance(HabitRepository habitRepository) {
    return new MainViewModel(habitRepository);
  }
}
