package com.example.habits9.ui.home;

import com.example.habits9.data.HabitRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HomeViewModel_Factory implements Factory<HomeViewModel> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  public HomeViewModel_Factory(Provider<HabitRepository> habitRepositoryProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
  }

  @Override
  public HomeViewModel get() {
    return newInstance(habitRepositoryProvider.get());
  }

  public static HomeViewModel_Factory create(Provider<HabitRepository> habitRepositoryProvider) {
    return new HomeViewModel_Factory(habitRepositoryProvider);
  }

  public static HomeViewModel newInstance(HabitRepository habitRepository) {
    return new HomeViewModel(habitRepository);
  }
}
